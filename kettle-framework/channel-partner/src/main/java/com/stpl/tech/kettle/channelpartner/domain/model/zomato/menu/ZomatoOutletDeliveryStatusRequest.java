package com.stpl.tech.kettle.channelpartner.domain.model.zomato.menu;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.fasterxml.jackson.annotation.JsonPropertyOrder;

@JsonInclude(JsonInclude.Include.NON_NULL)
@JsonPropertyOrder({
        "outlet_id",
        "outlet_delivery_status",
        "outlet_delivery_status_update_reason"
})
public class ZomatoOutletDeliveryStatusRequest {

    @JsonProperty("outlet_id")
    private String outletId;
    @JsonProperty("outlet_delivery_status")
    private Integer outletDeliveryStatus;
    @JsonProperty("outlet_delivery_status_update_reason")
    private String outletDeliveryStatusUpdateReason;

    @JsonProperty("outlet_id")
    public String getOutletId() {
        return outletId;
    }

    @JsonProperty("outlet_id")
    public void setOutletId(String outletId) {
        this.outletId = outletId;
    }

    @JsonProperty("outlet_delivery_status")
    public Integer getOutletDeliveryStatus() {
        return outletDeliveryStatus;
    }

    @JsonProperty("outlet_delivery_status")
    public void setOutletDeliveryStatus(Integer outletDeliveryStatus) {
        this.outletDeliveryStatus = outletDeliveryStatus;
    }

    @JsonProperty("outlet_delivery_status_update_reason")
    public String getOutletDeliveryStatusUpdateReason() {
        return outletDeliveryStatusUpdateReason;
    }

    @JsonProperty("outlet_delivery_status_update_reason")
    public void setOutletDeliveryStatusUpdateReason(String outletDeliveryStatusUpdateReason) {
        this.outletDeliveryStatusUpdateReason = outletDeliveryStatusUpdateReason;
    }
}

/*
 * SUNSHINE TEAHOUSE PRIVATE LIMITED CONFIDENTIAL
 * __________________
 *
 * [2015] - [2017] Sunshine Teahouse Private Limited
 * All Rights Reserved.
 *
 * NOTICE:  All information contained herein is, and remains
 * the property of Sunshine Teahouse Private Limited and its suppliers,
 * if any.  The intellectual and technical concepts contained
 * herein are proprietary to Sunshine Teahouse Private Limited
 * and its suppliers, and are protected by trade secret or copyright law.
 * Dissemination of this information or reproduction of this material
 * is strictly forbidden unless prior written permission is obtained
 * from Sunshine Teahouse Private Limited.
 */

(function () {
    'use strict';
    var regex = /^\d+$/;
    customerApp.controller("DashboardController", function ($rootScope, $scope, $http, $location, APIJson, $timeout, socketUtils) {

        $scope.onlyNumbers = /^[0-9]*$/;

        $scope.$on("$destroy", function () {
            socketUtils.removeAllListeners();
        });

        socketUtils.receiveMessage(function (message) {
            //console.log("Received message :::: ", message);
            processSocketMessage(message);
        });

        socketUtils.pairingDone(function (message) {
            //console.log("Received message :::: ", message);
        });

        socketUtils.pairingFailed(function (message) {
            //console.log("Received message :::: ", message);
        });

        function processSocketMessage(message) {
            message = JSON.parse(message);
            var key = Object.keys(message)[0];
            var customerObj = message[key];
            switch (key) {
                case "ORDER_START":
                    $scope.userObj = {};
                    $scope.createUserObj();
                    $scope.showUserContactScreen();
                    break;
                case "ORDER_PLACED":
                    $scope.userObj = {};
                    $scope.createUserObj();
                    showToast('Order placed successfully. Thanks for visiting Chaayos!', 4000);
                    $timeout(function () {
                        $("#userFlowView").focus();
                    }, 100);
                    $timeout(function () {
                        $scope.resetCustomerScreen();
                    }, 5000);
                    break;
                case "ORDER_IN_PROGRESS":
                    if (customerObj.name != null) {
                        showToast('We are placing your order. Thanks for visiting Chaayos!', 4000);
                        $scope.resetCustomerScreen();
                    } else {
                        $scope.fetchNameOnly();
                    }
                    break;
                case "CUSTOMER_NOT_INTERESTED" :
                    $scope.fetchNameOnly();
                    break;
                case "ORDER_CANCELLED":
                    if (customerObj.newCustomer) {
                        //contactVerified is set to false in the object from posCtrl.js itself
                        //TODO add a call to update contact verified equals to false
                        $scope.overrideContactVerification();
                    }
                    $scope.userObj = {};
                    $scope.createUserObj();
                    $timeout(function () {
                        $("#userFlowView").focus();
                    }, 100);
                    $scope.resetCustomerScreen();
                    $timeout.cancel($scope.thankYouTransition);
                    break;
                case "TABLE_ORDER":
                	$scope.userObj = {};
                    $scope.createUserObj();
                    $scope.userObj.contact = customerObj.contact;
                    $scope.lookUpContact();
                	break;
            }

        }

        $scope.init = function () {
            $("#contact,#otp").characterCounter();
            $scope.userObj = {};
            $scope.createUserObj();
            $rootScope.dataLoading = false;
            $scope.showEmailView = false;
            $scope.showEmailForm = true;
            $(".shadowBox").css("display", "none");
        }
        $scope.showUserContactScreen = function () {
            $scope.userObj.name = null;
            $scope.userObj.contact = null;
            $scope.OTPDisabled = true;
            $scope.userNameDisabled = true;
            $scope.showOTP = true;
            $scope.showContact = true;
            $scope.showEditContact = true;
            $scope.showUpdateUsername = false;
            $scope.showEmailForm = true;
            $scope.OtpSentCount = 0;
            $scope.showPromotionHead = false;
            $scope.exisitingUserNameUpdate = false;
            document.getElementById("userContactScreen").style.opacity = "1";
            document.getElementById("userContactScreen").style.zIndex = "9";
            document.getElementById("thankYouScreen").style.opacity = "0";
            document.getElementById("thankYouScreen").style.zIndex = "0";
            document.getElementById("redemptionScreen").style.opacity = "0";
            document.getElementById("redemptionScreen").style.zIndex = "0";
            document.getElementById("OTPScreen").style.opacity = "0";
            document.getElementById("OTPScreen").style.zIndex = "0";
            document.getElementById("userFlowView").style.opacity = "1";
            document.getElementById("userFlowView").style.zIndex = "3";
            $timeout(function () {
                $("#contact").focus();
            }, 500);
        }

        $scope.lookUpContact = function () {
            if ($scope.userObj.contact == null || ($scope.userObj.contact != null && $scope.userObj.contact.toString().length <= 1 && ($scope.userObj.contact > 9 || $scope.userObj.contact < 7))) {
                $scope.userObj.contact = null;
            } else if ($scope.userObj.contact.toString().length == 10) {
                $rootScope.dataLoading = true;
                socketUtils.emitMessage({PROCESS_STARTED: $scope.userObj});
                $http({
                    method: 'POST',
                    url: APIJson.urls.customer.signin,
                    data: $scope.userObj
                }).then(function success(response) {
                    $scope.userObj = response.data;
                    socketUtils.emitMessage({DETAILS_ENTERED: $scope.userObj});
                    $rootScope.dataLoading = false;
                    if (!$scope.userObj.contactVerified) {
                        $scope.showEditContact = false;
                        showToast('Please fill your name and OTP recieved on your number!', 5000);
                        socketUtils.emitMessage({NEW_CUSTOMER: $scope.userObj});
                        $scope.OTPDisabled = false;
                        $scope.userNameDisabled = false;
                        $scope.OtpSentCount += 1;
                        $timeout(function () {
                            $("#user").focus();
                        }, 100);
                    } else if ($scope.userObj.name == null || $scope.userObj.name == "") {
                        $scope.showOTP = false;
                        $scope.showUpdateUsername = true;
                        $scope.showEditContact = false;
                        $scope.userNameDisabled = false;
                        $scope.showUpdateUsername = true;
                        $scope.exisitingUserNameUpdate = true;
                        $timeout(function () {
                            $("#user").focus();
                        }, 100);
                    } else {
                        $scope.showRedeemScreen('fromNewUser');
                    }
                }, function error(response) {
                    //console.log("error:" + response);
                    $rootScope.dataLoading = false;
                });
            }
        }

        $scope.fetchNameOnly = function () {
            $scope.showContact = false;
            $scope.userNameDisabled = false;
            $scope.showOTP = false;
            $scope.showEditContact = true;
            $scope.showUpdateUsername = true;
            $timeout(function () {
                $("#user").focus();
            }, 100);
        }

        $scope.verifyCustomer = function () {
            if (document.getElementById("otp").value.length == 4) {
                $scope.verifyNumber();
            } else if ($scope.userObj.otp.toString().length > 4) {
                showToast('OTP should not be more than 4 digits!', 4000);
            }
        }

        $scope.updateUsername = function () {
            if ($scope.userObj.name == null || $scope.userObj.name == "") {
                showToast('Please enter user name!', 4000);
            } else {
                if (!$scope.showContact) {
                    socketUtils.emitMessage({DETAILS_ENTERED: $scope.userObj});
                    $scope.loadThankYouView();
                    $scope.showUpdateUsername = false;
                    $scope.hideEmailView();
                    showToast('Thanks for visiting Chaayos. We are placing your order!', 4000);
                } else {
                    $scope.userObj.name = toTitleCase($scope.userObj.name);
                    $rootScope.dataLoading = true;
                    $http({
                        method: 'POST',
                        url: APIJson.urls.customer.updateName,
                        data: $scope.userObj
                    }).then(function success(response) {
                        $rootScope.dataLoading = false;
                        $scope.userObj = response.data;
                        if ($scope.userObj.name == null || $scope.userObj.name == "") {
                            showToast('Connection Problem. Please try again!', 4000);
                        } else if ($scope.exisitingUserNameUpdate) {
                            $scope.showRedeemScreen('fromNewUser');
                        } else {
                            $scope.userObj.chaiRedeemed = 1;
                            $scope.userObj.productId = 10;
                            socketUtils.emitMessage({REDEMPTION: $scope.userObj});
                            $scope.showRedeemScreen("fromOTP");
                        }
                        socketUtils.emitMessage({DETAILS_ENTERED: $scope.userObj});
                    }, function error(response) {
                        //console.log("error:" + response);
                        $rootScope.dataLoading = false;
                    });
                }
            }
        }

        $scope.showRedeemScreen = function (source) {
            socketUtils.emitMessage({REDEMPTION_AVAILABLE: $scope.userObj});
            document.getElementById("userContactScreen").style.opacity = "0";
            document.getElementById("userContactScreen").style.zIndex = "0";
            document.getElementById("redemptionScreen").style.opacity = "1";
            document.getElementById("redemptionScreen").style.zIndex = "9";
            $("#userFlowView").focus();
            if (source == "fromOTP") {
                $scope.newUser = true;
                $scope.showRedemptionVerify = false;
                $scope.thankYouTransition = $timeout(function () {
                    showToast('Thanks for visiting Chaayos. We are placing your order!', 4000);
                    $scope.loadThankYouView();
                }, 10000);
            } else if (source == "fromNewUser") {
                $scope.newUser = false;
                var count = parseInt($scope.userObj.loyalityPoints / 60);
                $scope.chaiCount = count > 5 ? 5 : count;
                if (count > 0) {
                    $scope.chaiArray = [];
                    for (var i = 1; i <= $scope.chaiCount; i++) {
                        $scope.chaiArray.push(i);
                    }
                    $scope.showRedemptionVerify = true;
                } else {
                    $scope.thankYouTransition = $timeout(function () {
                        showToast('Thanks for visiting Chaayos. We are placing your order!', 4000);
                        $scope.loadThankYouView();
                    }, 10000);
                }
            }
        }

        $scope.loadOTPView = function () {
            socketUtils.emitMessage({OTP_SENT: $scope.userObj});
            $scope.showUserName = false;
            $scope.userObj.otp = null;
            document.getElementById("userContactScreen").style.opacity = "0";
            document.getElementById("userContactScreen").style.zIndex = "0";
            document.getElementById("OTPScreen").style.opacity = "1";
            document.getElementById("OTPScreen").style.zIndex = "9";
            $scope.OtpSentCount = 1;
            $timeout(function () {
                $("#redemptionotp").focus();
            }, 500);
        }

        $scope.editContact = function () {
            $scope.createUserObj();
            $scope.showEditContact = true;
        }

        $scope.resendAuthorizationOTP = function (signupOtp) {
            if ($scope.OtpSentCount == 2) {
                showToast('2nd last attempt!', 4000);
            }
            if ($scope.OtpSentCount == 3) {
                showToast('Last attempt!', 4000);
            }
            $rootScope.dataLoading = true;
            $http({
                method: 'POST',
                url: APIJson.urls.customer.resendAuthorizationOTP,
                data: $scope.userObj
            }).then(function success(response) {
                socketUtils.emitMessage({OTP_RESENT: $scope.userObj});
                $rootScope.dataLoading = false;
                if (response.data == true) {
                    showToast('OTP resent successfully. Please check your mobile!', 4000);
                    $scope.OtpSentCount += 1;
                    $timeout(function () {
                        $("#otp").focus();
                    }, 100);
                } else {
                    showToast('Error sending OTP. Please try again!', 4000);
                }
            }, function error(response) {
                //console.log("error:" + response);
                $rootScope.dataLoading = false;
            });
        }

        $scope.resendRedemptionOTP = function () {
            if ($scope.OtpSentCount == 2) {
                showToast('2nd last attempt!', 4000);
            }
            if ($scope.OtpSentCount == 3) {
                showToast('Last attempt!', 4000);
            }
            $rootScope.dataLoading = true;
            $http({
                method: 'POST',
                url: APIJson.urls.customer.resendRedemptionOTP,
                data: $scope.userObj
            }).then(function success(response) {
                $rootScope.dataLoading = false;
                if (response.data == true) {
                    socketUtils.emitMessage({OTP_RESENT: $scope.userObj});
                    showToast('OTP resent successfully. Please check your mobile!', 4000);
                    $scope.OtpSentCount += 1;
                    $timeout(function () {
                        $("#redemptionotp").focus();
                    }, 100);
                } else {
                    showToast('Error sending OTP. Please try again!', 4000);
                }
            }, function error(response) {
                //console.log("error:" + response);
                $rootScope.dataLoading = false;
            });
        }

        $scope.verifyNumber = function () {
            $rootScope.dataLoading = true;
            $scope.userObj.otp = document.getElementById("otp").value;
            if ($scope.userObj.name != null && $scope.userObj.name != "") {
                $scope.userObj.name = toTitleCase($scope.userObj.name);
            }
            socketUtils.emitMessage({OTP_SUBMITTED: $scope.userObj});
            $http({
                method: 'POST',
                url: APIJson.urls.customer.signup,
                data: $scope.userObj
            }).then(function success(response) {
                $rootScope.dataLoading = false;
                $scope.userObj = response.data;
                socketUtils.emitMessage({OTP_STATUS: $scope.userObj});
                if ($scope.userObj.contactVerified) {
                    if ($scope.userObj.name == null || $scope.userObj.name == "") {
                        $scope.showOTP = false;
                        $scope.showUpdateUsername = true;
                        $timeout(function () {
                            $("#user").focus();
                        }, 100);
                    } else {
                        $scope.userObj.chaiRedeemed = 1;
                        $scope.userObj.productId = 10;
                        socketUtils.emitMessage({REDEMPTION: $scope.userObj});
                        $scope.showRedeemScreen("fromOTP");
                    }
                } else {
                    showToast('Incorrect OTP. Please enter again!', 4000);
                    $scope.userObj.otp = null;
                }
            }, function error(response) {
                //console.log("error:" + response);
                $rootScope.dataLoading = false;
            });
        }

        $scope.verifyRedemption = function () {
            if (document.getElementById("redemptionotp").value.length == 4) {
                $rootScope.dataLoading = true;
                $scope.userObj.otp = document.getElementById("redemptionotp").value;
                $http({
                    method: 'POST',
                    url: APIJson.urls.customer.verifyOTP,
                    data: $scope.userObj
                }).then(function success(response) {
                    if (response.data) {
                        if ($scope.chaiRedeemed > 0) {
                            $scope.userObj.chaiRedeemed = $scope.chaiRedeemed;
                            $scope.userObj.productId = 10;
                            socketUtils.emitMessage({REDEMPTION: $scope.userObj});
                            var cup = $scope.chaiRedeemed > 1 ? "cups" : "cup";
                            showToast('We have added ' + $scope.chaiRedeemed + ' ' + cup + ' of Meri Wali Desi Chai to your order!', 10000);
                        } else {
                            showToast('Thanks for visiting Chaayos. We are placing your order!', 10000);
                        }
                        $scope.loadThankYouView();
                        $timeout(function () {
                            $("#userFlowView").focus();
                        }, 100);
                    } else {
                        showToast('Incorrect OTP. Please enter again!', 4000);
                        $scope.userObj.otp = null;
                    }
                    $rootScope.dataLoading = false;
                }, function error(response) {
                    //console.log("error:" + response);
                    $rootScope.dataLoading = false;
                });
            }
        }

        $scope.redeemChai = function (count) {
            $scope.chaiRedeemed = count;
            $scope.userObj.chaiRedeemed = $scope.chaiRedeemed;
            $scope.userObj.productId = 10;
            if ($scope.showRedemptionVerify && $scope.chaiRedeemed > 0) {
                $rootScope.dataLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.customer.generateOTP,
                    data: $scope.userObj
                }).then(function success(response) {
                    if (response.data) {
                        document.getElementById("redemptionScreen").style.opacity = "0";
                        document.getElementById("redemptionScreen").style.zIndex = "0";
                        $scope.loadOTPView();
                        $rootScope.dataLoading = false;
                    } else {
                        showToast('Network problem. Please try again!', 4000);
                    }
                }, function error(response) {
                    //console.log("error:" + response);
                    $rootScope.dataLoading = false;
                });
            } else {
                if ($scope.chaiRedeemed > 0) {
                    socketUtils.emitMessage({REDEMPTION: $scope.userObj});
                    var cup = $scope.chaiRedeemed > 1 ? "cups" : "cup";
                    showToast('We have added ' + $scope.chaiRedeemed + ' ' + cup + ' of Meri Wali Desi Chai to your order!', 10000);
                } else {
                    socketUtils.emitMessage({REDEEM_LATER: $scope.userObj});
                    showToast('Thanks for visiting Chaayos. We are placing your order!', 10000);
                }
                $scope.loadThankYouView();
            }
        }

        $scope.setEmail = function () {
            if ($scope.userObj.email == null || $scope.userObj.email == "" || !validEmail($scope.userObj.email)) {
                showToast('Please fill proper email address!', 4000);
            } else {
                $rootScope.dataLoading = true;
                $http({
                    method: 'POST',
                    url: APIJson.urls.customer.updateName,
                    data: $scope.userObj
                }).then(function success(response) {
                    $rootScope.dataLoading = false;
                    $scope.userObj = response.data;
                    if ($scope.userObj.email == null || $scope.userObj.email == "") {
                        showToast('Connection Problem. Please try again!', 4000);
                    } else {
                        socketUtils.emitMessage({EMAIL_ENTERED: $scope.userObj});
                        $scope.showEmailForm = false;
                        $timeout(function () {
                            $scope.hideEmailView();
                        }, 10000);
                    }
                }, function error(response) {
                    //console.log("error:" + response);
                    $rootScope.dataLoading = false;
                });
            }
        }

        $scope.loadThankYouView = function () {
            $scope.loadEmailView();
            if (document.getElementById("redemptionScreen").style.opacity == "1") {
                document.getElementById("redemptionScreen").style.opacity = "0";
                document.getElementById("redemptionScreen").style.zIndex = "0";
            }
            if (document.getElementById("OTPScreen").style.opacity == "1") {
                document.getElementById("OTPScreen").style.opacity = "0";
                document.getElementById("OTPScreen").style.zIndex = "0";
            }
            document.getElementById("thankYouScreen").style.opacity = "1";
            document.getElementById("thankYouScreen").style.zIndex = "9";
        }

        $scope.loadEmailView = function () {
            if ($scope.userObj.email == null || $scope.userObj.email == "") {
                $scope.bgImage = {"background-image": "url('img/emailBg.jpg')"};
                $scope.showEmailView = true;
            } else {
                $scope.showPromotionHead = true;
            }
        }

        $scope.hideEmailView = function () {
            $scope.showEmailView = false;
            $scope.showPromotionHead = true;
        }

        $scope.resetCustomerScreen = function () {
            $scope.hideEmailView();
            $scope.createUserObj();
            document.getElementById("userFlowView").style.opacity = "0";
            document.getElementById("userFlowView").style.zIndex = "0";
            $(".shadowBox").css("display", "none");
            $("#toast-container").html("");
        }

        $scope.overrideContactVerification = function () {
            $scope.userObj.name = $scope.userObj.name != null ? toTitleCase($scope.userObj.name) : "";
            $scope.userObj.contactVerified = false;
            $rootScope.dataLoading = true;
            $http({
                method: 'POST',
                url: APIJson.urls.customer.overrideContactVerification,
                data: $scope.userObj
            }).then(function success(response) {
                $rootScope.dataLoading = false;
                $scope.userObj = response.data;
            }, function error(response) {
                //console.log("error:" + response);
                $rootScope.dataLoading = false;
            });
        }

        $scope.createUserObj = function () {
            $scope.userObj = {
                id: null,
                name: null,
                contact: null,
                email: null,
                loyalityPoints: null,
                contactVerified: null,
                emailVerified: null,
                unitId: $rootScope.loginObj.unitId,
                newCustomer: null,
                otp: null,
                chaiRedeemed: 0,
                productId: 10
            };
        }

        $scope.hideShadow = function () {
            $(".shadowBox").css("display", "none");
            $("#toast-container").html("");
        }

        function validEmail(email) {
            var re = /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
            return re.test(email);
        }

        function toTitleCase(str) {
            return str.replace(/\w\S*/g, function (txt) {
                return txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase();
            });
        }

        function showToast(msg, time) {
            $("#toast-container").html("");
            $(".shadowBox").css("display", "block");
            Materialize.toast(msg, time, '', function () {
                $(".shadowBox").css("display", "none");
            });
        }
    }).directive('restrictInput', [function () {

        return {
            restrict: 'A',
            link: function (scope, element, attrs) {
                var ele = element[0];
                var value = ele.value;
                ele.addEventListener('keyup', function (e) {
                    if (ele.value == null || ele.value == '' ||  regex.test(ele.value)) {
                        value = ele.value;
                    } else {
                        ele.value = value;
                    }
                });
            }
        };
    }]);
})();
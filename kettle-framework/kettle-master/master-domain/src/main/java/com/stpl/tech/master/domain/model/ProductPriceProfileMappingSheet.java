package com.stpl.tech.master.domain.model;


import com.stpl.tech.util.excelparser.annotations.ExcelField;
import com.stpl.tech.util.excelparser.annotations.ExcelObject;
import com.stpl.tech.util.excelparser.annotations.ParseType;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;

import java.math.BigDecimal;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@ExcelObject(parseType = ParseType.ROW, start = 1, end = 0)
public class ProductPriceProfileMappingSheet {

    @ExcelField
    String priceProfileName;


    @ExcelField
    Integer priceProfileVersion;

    @ExcelField
    Integer productId;


    @ExcelField
    String productName;


    @ExcelField
    String dimension;

    @ExcelField
    BigDecimal price;


    @ExcelField
    String status;

}
